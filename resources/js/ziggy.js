const Ziggy = {"url":"http:\/\/localhost:8000","port":8000,"defaults":{},"routes":{"home":{"uri":"\/","methods":["GET","HEAD"]},"dashboard":{"uri":"dashboard","methods":["GET","HEAD"]},"distributions.index":{"uri":"distributions","methods":["GET","HEAD"]},"distributions.create":{"uri":"distributions\/create","methods":["GET","HEAD"]},"distributions.store":{"uri":"distributions","methods":["POST"]},"distributions.show":{"uri":"distributions\/{distribution}","methods":["GET","HEAD"],"parameters":["distribution"],"bindings":{"distribution":"id"}},"children":{"uri":"children","methods":["GET","HEAD"]},"children.create":{"uri":"children\/create","methods":["GET","HEAD"]},"children.store":{"uri":"children","methods":["POST"]},"children.show":{"uri":"children\/{id}","methods":["GET","HEAD"],"parameters":["id"]},"sponsorships.index":{"uri":"sponsorships","methods":["GET","HEAD"]},"sponsorships.create":{"uri":"sponsorships\/create","methods":["GET","HEAD"]},"sponsorships.store":{"uri":"sponsorships","methods":["POST"]},"sponsorships.show":{"uri":"sponsorships\/{sponsorship}","methods":["GET","HEAD"],"parameters":["sponsorship"],"bindings":{"sponsorship":"id"}},"progress-reports.index":{"uri":"progress-reports","methods":["GET","HEAD"]},"progress-reports.create":{"uri":"progress-reports\/create","methods":["GET","HEAD"]},"progress-reports.store":{"uri":"progress-reports","methods":["POST"]},"progress-reports.show":{"uri":"progress-reports\/{progress_report}","methods":["GET","HEAD"],"parameters":["progress_report"]},"receipts.index":{"uri":"receipts","methods":["GET","HEAD"]},"receipts.show":{"uri":"receipts\/{receipt}","methods":["GET","HEAD"],"parameters":["receipt"],"bindings":{"receipt":"id"}},"thank-you-letters.index":{"uri":"thank-you-letters","methods":["GET","HEAD"]},"thank-you-letters.show":{"uri":"thank-you-letters\/{thank_you_letter}","methods":["GET","HEAD"],"parameters":["thank_you_letter"]},"admin.dashboard":{"uri":"admin\/dashboard","methods":["GET","HEAD"]},"secretary.dashboard":{"uri":"secretary\/dashboard","methods":["GET","HEAD"]},"secretary.children":{"uri":"secretary\/children","methods":["GET","HEAD"]},"secretary.children.show":{"uri":"secretary\/children\/{id}","methods":["GET","HEAD"],"parameters":["id"]},"secretary.children.store":{"uri":"secretary\/children","methods":["POST"]},"sponsor.dashboard":{"uri":"sponsor\/dashboard","methods":["GET","HEAD"]},"sponsor.children":{"uri":"sponsor\/children","methods":["GET","HEAD"]},"sponsor.child":{"uri":"sponsor\/children\/{id}","methods":["GET","HEAD"],"parameters":["id"]},"sponsor.sponsor-child":{"uri":"sponsor\/children\/{id}\/sponsor","methods":["POST"],"parameters":["id"]},"sponsor.my-sponsorships":{"uri":"sponsor\/my-sponsorships","methods":["GET","HEAD"]},"donor.dashboard":{"uri":"donor\/dashboard","methods":["GET","HEAD"]},"donor.donations":{"uri":"donor\/donations","methods":["GET","HEAD"]},"donor.children":{"uri":"donor\/children","methods":["GET","HEAD"]},"donor.donate":{"uri":"donor\/donate","methods":["GET","HEAD"]},"donor.history":{"uri":"donor\/history","methods":["GET","HEAD"]},"donor.child":{"uri":"donor\/children\/{id}","methods":["GET","HEAD"],"parameters":["id"]},"donor.donations.api":{"uri":"donor\/donations\/api","methods":["GET","HEAD"]},"donor.donations.store":{"uri":"donor\/donations","methods":["POST"]},"donor.donations.callback":{"uri":"donor\/donations\/callback","methods":["POST"]},"donor.donations.return":{"uri":"donor\/donations\/return","methods":["GET","HEAD"]},"donor.donations.verify":{"uri":"donor\/donations\/verify","methods":["POST"]},"donor.donation.success":{"uri":"donor\/donations\/success\/{ref}","methods":["GET","HEAD"],"parameters":["ref"]},"donor.donation.failed":{"uri":"donor\/donations\/failed\/{ref?}","methods":["GET","HEAD"],"parameters":["ref"]},"donor.donation.verify.page":{"uri":"donor\/donations\/verify","methods":["GET","HEAD"]},"inventory.dashboard":{"uri":"inventory\/dashboard","methods":["GET","HEAD"]},"inventory.children":{"uri":"inventory\/children","methods":["GET","HEAD"]},"inventory.children.show":{"uri":"inventory\/children\/{id}","methods":["GET","HEAD"],"parameters":["id"]},"inventory.children.update":{"uri":"inventory\/children\/{id}","methods":["PUT"],"parameters":["id"]},"inventory.children.destroy":{"uri":"inventory\/children\/{id}","methods":["DELETE"],"parameters":["id"]},"inventory.communications":{"uri":"inventory\/communications","methods":["GET","HEAD"]},"inventory.donations":{"uri":"inventory\/donations","methods":["GET","HEAD"]},"inventory.donations.add-to-inventory":{"uri":"inventory\/donations\/{donation}\/add-to-inventory","methods":["POST"],"parameters":["donation"]},"inventory.inventory":{"uri":"inventory\/inventory","methods":["GET","HEAD"]},"inventory.reports":{"uri":"inventory\/reports","methods":["GET","HEAD"]},"inventory.campaigns.index":{"uri":"inventory\/campaigns","methods":["GET","HEAD"]},"inventory.campaigns.create":{"uri":"inventory\/campaigns\/create","methods":["GET","HEAD"]},"inventory.campaigns.store":{"uri":"inventory\/campaigns","methods":["POST"]},"inventory.campaigns.show":{"uri":"inventory\/campaigns\/{campaign}","methods":["GET","HEAD"],"parameters":["campaign"],"bindings":{"campaign":"id"}},"accountant.dashboard":{"uri":"accountant\/dashboard","methods":["GET","HEAD"]},"accountant.donations":{"uri":"accountant\/donations","methods":["GET","HEAD"]},"accountant.campaigns":{"uri":"accountant\/campaigns","methods":["GET","HEAD"]},"accountant.campaigns.show":{"uri":"accountant\/campaigns\/{campaign}","methods":["GET","HEAD"],"parameters":["campaign"],"bindings":{"campaign":"id"}},"accountant.children":{"uri":"accountant\/children","methods":["GET","HEAD"]},"accountant.children.show":{"uri":"accountant\/children\/{child}","methods":["GET","HEAD"],"parameters":["child"],"bindings":{"child":"id"}},"campaigns.donate":{"uri":"campaigns\/{campaign}\/donate","methods":["GET","HEAD"],"parameters":["campaign"],"bindings":{"campaign":"id"}},"campaigns.donate.anonymous":{"uri":"campaigns\/{campaign}\/donate\/anonymous","methods":["POST"],"parameters":["campaign"],"bindings":{"campaign":"id"}},"campaigns.donate.authenticated":{"uri":"campaigns\/{campaign}\/donate-authenticated","methods":["GET","HEAD"],"parameters":["campaign"],"bindings":{"campaign":"id"}},"campaigns.donate.store":{"uri":"campaigns\/{campaign}\/donate","methods":["POST"],"parameters":["campaign"],"bindings":{"campaign":"id"}},"campaign.donation.success":{"uri":"campaigns\/donations\/{ref}\/success","methods":["GET","HEAD"],"parameters":["ref"]},"campaign.donation.callback":{"uri":"campaigns\/donations\/callback","methods":["POST"]},"profile.edit":{"uri":"settings\/profile","methods":["GET","HEAD"]},"profile.update":{"uri":"settings\/profile","methods":["PATCH"]},"profile.destroy":{"uri":"settings\/profile","methods":["DELETE"]},"password.edit":{"uri":"settings\/password","methods":["GET","HEAD"]},"password.update":{"uri":"settings\/password","methods":["PUT"]},"appearance":{"uri":"settings\/appearance","methods":["GET","HEAD"]},"register":{"uri":"register","methods":["GET","HEAD"]},"login":{"uri":"login","methods":["GET","HEAD"]},"password.request":{"uri":"forgot-password","methods":["GET","HEAD"]},"password.email":{"uri":"forgot-password","methods":["POST"]},"password.reset":{"uri":"reset-password\/{token}","methods":["GET","HEAD"],"parameters":["token"]},"password.store":{"uri":"reset-password","methods":["POST"]},"verification.notice":{"uri":"verify-email","methods":["GET","HEAD"]},"verification.verify":{"uri":"verify-email\/{id}\/{hash}","methods":["GET","HEAD"],"parameters":["id","hash"]},"verification.send":{"uri":"email\/verification-notification","methods":["POST"]},"password.confirm":{"uri":"confirm-password","methods":["GET","HEAD"]},"logout":{"uri":"logout","methods":["POST"]},"anonymous.donation":{"uri":"anonymous-donation","methods":["GET","HEAD"]},"anonymous.donation.verify.page":{"uri":"anonymous-donation\/verify","methods":["GET","HEAD"]},"anonymous.donation.store":{"uri":"anonymous-donation","methods":["POST"]},"anonymous.donation.callback":{"uri":"anonymous-donation\/callback","methods":["POST"]},"anonymous.donation.return":{"uri":"anonymous-donation\/return","methods":["GET","HEAD"]},"anonymous.donation.verify":{"uri":"anonymous-donation\/verify","methods":["POST"]},"anonymous.donation.success":{"uri":"anonymous-donation\/success\/{ref}","methods":["GET","HEAD"],"parameters":["ref"]},"anonymous.donation.failed":{"uri":"anonymous-donation\/failed\/{ref?}","methods":["GET","HEAD"],"parameters":["ref"]},"admin.users.index":{"uri":"admin\/users","methods":["GET","HEAD"]},"admin.users.create":{"uri":"admin\/users\/create","methods":["GET","HEAD"]},"admin.users.store":{"uri":"admin\/users","methods":["POST"]},"admin.users.edit":{"uri":"admin\/users\/{user}\/edit","methods":["GET","HEAD"],"parameters":["user"]},"admin.users.activate":{"uri":"admin\/users\/{user}\/activate","methods":["POST"],"parameters":["user"],"bindings":{"user":"id"}},"admin.users.deactivate":{"uri":"admin\/users\/{user}\/deactivate","methods":["POST"],"parameters":["user"],"bindings":{"user":"id"}},"storage.local":{"uri":"storage\/{path}","methods":["GET","HEAD"],"wheres":{"path":".*"},"parameters":["path"]}}};
if (typeof window !== 'undefined' && typeof window.Ziggy !== 'undefined') {
  Object.assign(Ziggy.routes, window.Ziggy.routes);
}
export { Ziggy };
