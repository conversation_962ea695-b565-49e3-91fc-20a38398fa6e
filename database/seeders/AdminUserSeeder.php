<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class AdminUserSeeder extends Seeder
{
    public function run(): void
    {
        DB::table('users')->insert([
            'name' => 'Admin User',
            'email' => '<EMAIL>',
            'email_verified_at' => now(),
            'password' => Hash::make('admin123'),
            'phone' => '************',
            'role' => 'admin',
            'profile_photo' => null,
            'sponsor_type' => null,
            'organization_name' => null,
            'remember_token' => Str::random(10),
            'created_at' => now(),
            'updated_at' => now(),
        ]);
    }
}
